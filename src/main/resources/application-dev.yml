server:
    port: 8081

# 28181 SIP服务器的配置
sip-server:
    # [必须修改] SIP服务器的IP（如果是本机启动的SIP服务器，就是本机的ip地址）
    ip: ************
    # 28181服务监听的端口
    port: 15060
    # 根据国标6.1.2中规定，domain宜采用ID统一编码的前十位编码。国标附录D中定义前8位为中心编码（由省级、市级、区级、基层编号组成，参照GB/T 2260-2007）
    # 后两位为行业编码，定义参照附录D.3
    # 3701020049标识山东济南历下区 信息行业接入
    domain: ${WVP_DOMAIN:3402000000}
    id: ${WVP_ID:3402000000200000001}
    # [可选] 默认设备认证密码，后续扩展使用设备单独密码, 移除密码将不进行校验
    password: ${WVP_PWD:12345678}
    #心跳周期
    keepaliveTimeOut: 30

#作为28181设备的配置
sip-device:
    # [必须修改] 本机的IP（项目部署的机器的IP地址，本地部署，就是本地机器IP）
    ip: *************
    # 28181服务监听的端口
    port: 5080
    #
    id: 64010000001110000001
    #默认设备认证密码
    password: 12345678

ffmpeg:
    #ffmpeg执行文件路径
    path: C:\Program Files\ffmpeg-7.1.1-full_build\bin\ffmpeg.exe
    #模拟推送视频流的命令
    pushStreamCmd: -re -i {filePath} -vcodec h264 -acodec aac -f rtp_mpegts -r 25 -s 1920x1080 -b:v 2048k -bufsize 2048k -maxrate 2048k rtp://{ip}:{port}